# SkyWalking 迁移指南

## 📋 迁移步骤

### 1. 下载 SkyWalking Agent
```bash
# 下载 SkyWalking Agent
wget https://archive.apache.org/dist/skywalking/8.16.0/apache-skywalking-apm-8.16.0.tar.gz
tar -xzf apache-skywalking-apm-8.16.0.tar.gz
```

### 2. 修改 Docker Compose 配置

```yaml
# docker-compose-skywalking.yml
version: '3.8'

services:
  # SkyWalking OAP Server
  skywalking-oap:
    image: apache/skywalking-oap-server:8.16.0
    container_name: skywalking-oap
    ports:
      - "11800:11800"  # gRPC
      - "12800:12800"  # HTTP
    environment:
      SW_STORAGE: elasticsearch
      SW_STORAGE_ES_CLUSTER_NODES: elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - monitoring

  # SkyWalking UI
  skywalking-ui:
    image: apache/skywalking-ui:8.16.0
    container_name: skywalking-ui
    ports:
      - "8080:8080"
    environment:
      SW_OAP_ADDRESS: http://skywalking-oap:12800
    depends_on:
      - skywalking-oap
    networks:
      - monitoring

  # Elasticsearch for SkyWalking storage
  elasticsearch:
    image: elasticsearch:7.17.0
    container_name: skywalking-elasticsearch
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    networks:
      - monitoring

networks:
  monitoring:
    driver: bridge
```

### 3. 修改应用启动配置

```bash
# 启动脚本示例
java -javaagent:/path/to/skywalking-agent/skywalking-agent.jar \
     -Dskywalking.agent.service_name=user-center \
     -Dskywalking.collector.backend_service=localhost:11800 \
     -jar user-center.jar
```

### 4. Maven 配置修改

```xml
<!-- 移除 Zipkin 相关依赖 -->
<!--
<dependency>
    <groupId>io.micrometer</groupId>
    <artifactId>micrometer-tracing-bridge-otel</artifactId>
</dependency>
<dependency>
    <groupId>io.opentelemetry</groupId>
    <artifactId>opentelemetry-exporter-zipkin</artifactId>
</dependency>
-->

<!-- 可选：添加 SkyWalking 工具包 -->
<dependency>
    <groupId>org.apache.skywalking</groupId>
    <artifactId>apm-toolkit-trace</artifactId>
    <version>8.16.0</version>
</dependency>
```

### 5. 配置文件修改

```yaml
# application.yml - 移除 Zipkin 配置
management:
  # 移除 zipkin 配置
  # zipkin:
  #   tracing:
  #     endpoint: http://localhost:9411/api/v2/spans
  
  # 保留其他监控配置
  tracing:
    sampling:
      probability: 0.1
```

### 6. 代码修改（可选）

```java
// 使用 SkyWalking 注解替代 @Observed
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.apache.skywalking.apm.toolkit.trace.Tag;

@Service
public class UserService {
    
    @Trace(operationName = "updateUserProfile")
    @Tag(key = "userId", value = "arg[0]")
    public void updateUserProfile(Long userId, UserProfileDTO profile) {
        // 业务逻辑
    }
}
```

## 🔧 配置对比

### Zipkin 配置 (当前)
```yaml
management:
  tracing:
    sampling:
      probability: 0.1
  zipkin:
    tracing:
      endpoint: http://localhost:9411/api/v2/spans
```

### SkyWalking 配置 (迁移后)
```bash
# Agent 配置
-Dskywalking.agent.service_name=user-center
-Dskywalking.collector.backend_service=localhost:11800
-Dskywalking.agent.sample_n_per_3_secs=9  # 采样配置
```

## 📊 功能对比

| 功能 | Zipkin (当前) | SkyWalking (迁移后) |
|------|---------------|---------------------|
| 链路追踪 | ✅ | ✅ |
| 服务拓扑 | ✅ 基础 | ✅ 详细 |
| 性能监控 | ❌ | ✅ |
| JVM监控 | ❌ | ✅ |
| 数据库监控 | ❌ | ✅ |
| 告警功能 | ❌ | ✅ |
| 无侵入性 | ❌ | ✅ |

## ⚠️ 迁移注意事项

1. **数据不兼容**: Zipkin 和 SkyWalking 数据格式不同，无法直接迁移历史数据
2. **资源需求**: SkyWalking 需要更多内存和存储空间
3. **学习成本**: 团队需要学习新的UI和概念
4. **部署复杂度**: SkyWalking 部署相对复杂

## 🎯 建议

基于您当前的项目情况，建议：

1. **保持现状**: 当前 Zipkin + Prometheus + Grafana 组合已经很好
2. **渐进式评估**: 可以在测试环境先试用 SkyWalking
3. **混合使用**: 可以同时运行两套系统进行对比
4. **团队决策**: 根据团队技能和项目需求决定

## 🚀 快速试用 SkyWalking

如果想快速体验 SkyWalking，可以：

```bash
# 1. 启动 SkyWalking (简化版)
docker run -d --name skywalking-oap \
  -p 11800:11800 -p 12800:12800 \
  apache/skywalking-oap-server:8.16.0

docker run -d --name skywalking-ui \
  -p 8080:8080 \
  -e SW_OAP_ADDRESS=http://skywalking-oap:12800 \
  apache/skywalking-ui:8.16.0

# 2. 下载 Agent
wget https://archive.apache.org/dist/skywalking/8.16.0/apache-skywalking-apm-8.16.0.tar.gz

# 3. 使用 Agent 启动应用
java -javaagent:skywalking-agent.jar \
     -Dskywalking.agent.service_name=user-center-test \
     -jar your-app.jar
```

这样可以在不影响现有系统的情况下评估 SkyWalking 的功能。
