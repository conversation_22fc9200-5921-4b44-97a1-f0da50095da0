package com.tinyzk.user.center.service;

import com.tinyzk.user.center.entity.OAuthClientDetails;

import java.util.List;

/**
 * 客户端服务接口，提供客户端相关的业务操作
 */
public interface ClientService {
    
    /**
     * 验证客户端是否有效
     *
     * @param clientId 客户端ID
     * @return 客户端是否有效
     */
    boolean validateClient(String clientId);
    
    /**
     * 获取客户端详情
     *
     * @param clientId 客户端ID
     * @return 客户端详情对象
     */
    OAuthClientDetails getClientDetails(String clientId);
    
    /**
     * 保存或更新客户端
     *
     * @param clientDetails 客户端详情对象
     * @return 操作是否成功
     */
    boolean saveOrUpdateClient(OAuthClientDetails clientDetails);
    
    /**
     * 禁用客户端
     *
     * @param clientId 客户端ID
     * @return 操作是否成功
     */
    boolean disableClient(String clientId);
    
    /**
     * 启用客户端
     *
     * @param clientId 客户端ID
     * @return 操作是否成功
     */
    boolean enableClient(String clientId);
    
    /**
     * 查询所有有效的客户端
     *
     * @return 客户端详情列表
     */
    List<OAuthClientDetails> listActiveClients();
} 