package com.tinyzk.user.center.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tinyzk.user.center.entity.OAuthClientDetails;
import com.tinyzk.user.center.mapper.OAuthClientDetailsMapper;
import com.tinyzk.user.center.service.ClientService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户端服务实现类
 */
@Service
public class ClientServiceImpl extends ServiceImpl<OAuthClientDetailsMapper, OAuthClientDetails> implements ClientService {

    /**
     * 验证客户端是否有效
     *
     * @param clientId 客户端ID
     * @return 客户端是否有效
     */
    @Override
    public boolean validateClient(String clientId) {
        if (clientId == null || clientId.isEmpty()) {
            return false;
        }
        
        LambdaQueryWrapper<OAuthClientDetails> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OAuthClientDetails::getClientId, clientId)
                .eq(OAuthClientDetails::getStatus, 1); // 状态为正常
        
        return this.count(queryWrapper) > 0;
    }

    /**
     * 获取客户端详情
     *
     * @param clientId 客户端ID
     * @return 客户端详情对象
     */
    @Override
    public OAuthClientDetails getClientDetails(String clientId) {
        if (clientId == null || clientId.isEmpty()) {
            return null;
        }
        
        return this.getById(clientId);
    }

    /**
     * 保存或更新客户端
     *
     * @param clientDetails 客户端详情对象
     * @return 操作是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateClient(OAuthClientDetails clientDetails) {
        if (clientDetails == null || clientDetails.getClientId() == null || clientDetails.getClientId().isEmpty()) {
            return false;
        }
        
        // 设置创建和更新时间
        if (this.getById(clientDetails.getClientId()) == null) {
            // 新增客户端
            clientDetails.setCreatedAt(LocalDateTime.now());
            clientDetails.setUpdatedAt(LocalDateTime.now());
            if (clientDetails.getStatus() == null) {
                clientDetails.setStatus(1); // 默认状态为正常
            }
        } else {
            // 更新客户端
            clientDetails.setUpdatedAt(LocalDateTime.now());
        }
        
        return this.saveOrUpdate(clientDetails);
    }

    /**
     * 禁用客户端
     *
     * @param clientId 客户端ID
     * @return 操作是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disableClient(String clientId) {
        if (clientId == null || clientId.isEmpty()) {
            return false;
        }
        
        OAuthClientDetails clientDetails = this.getById(clientId);
        if (clientDetails == null) {
            return false;
        }
        
        clientDetails.setStatus(0); // 设置状态为禁用
        clientDetails.setUpdatedAt(LocalDateTime.now());
        
        return this.updateById(clientDetails);
    }

    /**
     * 启用客户端
     *
     * @param clientId 客户端ID
     * @return 操作是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean enableClient(String clientId) {
        if (clientId == null || clientId.isEmpty()) {
            return false;
        }
        
        OAuthClientDetails clientDetails = this.getById(clientId);
        if (clientDetails == null) {
            return false;
        }
        
        clientDetails.setStatus(1); // 设置状态为正常
        clientDetails.setUpdatedAt(LocalDateTime.now());
        
        return this.updateById(clientDetails);
    }

    /**
     * 查询所有有效的客户端
     *
     * @return 客户端详情列表
     */
    @Override
    public List<OAuthClientDetails> listActiveClients() {
        LambdaQueryWrapper<OAuthClientDetails> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OAuthClientDetails::getStatus, 1); // 状态为正常
        
        return this.list(queryWrapper);
    }
} 