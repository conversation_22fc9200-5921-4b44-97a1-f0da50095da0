package com.tinyzk.user.center.service;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.time.Duration;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 业务指标服务
 * 负责收集和管理自定义业务指标
 */
@Slf4j
@Service
public class BusinessMetricsService {

    private final MeterRegistry meterRegistry;
    private final RedisTemplate<String, Object> redisTemplate;
    private final DataSource dataSource;

    // 计数器
    private final Counter userRegistrationCounter;
    private final Counter cacheHitCounter;
    private final Counter cacheMissCounter;

    // 计时器
    private final Timer databaseQueryTimer;
    private final Timer redisOperationTimer;
    private final Timer userAuthTimer;

    // 原子计数器用于实时指标
    private final AtomicLong activeUserSessions = new AtomicLong(0);
    private final AtomicLong pendingOperations = new AtomicLong(0);

    public BusinessMetricsService(MeterRegistry meterRegistry, 
                                RedisTemplate<String, Object> redisTemplate,
                                DataSource dataSource) {
        this.meterRegistry = meterRegistry;
        this.redisTemplate = redisTemplate;
        this.dataSource = dataSource;

        // 初始化计数器
        this.userRegistrationCounter = Counter.builder("user.registration.total")
                .description("Total number of user registrations")
                .register(meterRegistry);

        this.cacheHitCounter = Counter.builder("cache.hit.total")
                .description("Total number of cache hits")
                .register(meterRegistry);

        this.cacheMissCounter = Counter.builder("cache.miss.total")
                .description("Total number of cache misses")
                .register(meterRegistry);

        // 初始化计时器
        this.databaseQueryTimer = Timer.builder("database.query.duration")
                .description("Database query execution time")
                .register(meterRegistry);

        this.redisOperationTimer = Timer.builder("redis.operation.duration")
                .description("Redis operation execution time")
                .register(meterRegistry);

        this.userAuthTimer = Timer.builder("user.auth.duration")
                .description("User authentication time")
                .register(meterRegistry);

        // 注册Gauge指标
        registerGaugeMetrics();
    }

    /**
     * 注册Gauge指标
     */
    private void registerGaugeMetrics() {
        // 活跃用户会话数
        Gauge.builder("user.sessions.active", activeUserSessions, AtomicLong::doubleValue)
                .description("Number of active user sessions")
                .register(meterRegistry);

        // 待处理操作数
        Gauge.builder("operations.pending", pendingOperations, AtomicLong::doubleValue)
                .description("Number of pending operations")
                .register(meterRegistry);

        // 数据库连接池状态
        Gauge.builder("database.connections.active", this, BusinessMetricsService::getActiveDatabaseConnections)
                .description("Number of active database connections")
                .register(meterRegistry);

        // Redis连接状态
        Gauge.builder("redis.connections.active", this, BusinessMetricsService::getRedisConnectionStatus)
                .description("Redis connection status (1=connected, 0=disconnected)")
                .register(meterRegistry);

        // 缓存命中率
        Gauge.builder("cache.hit.rate", this, BusinessMetricsService::getCacheHitRate)
                .description("Cache hit rate percentage")
                .register(meterRegistry);

        // 用户总数
        Gauge.builder("user.total.count", this, BusinessMetricsService::getTotalUserCount)
                .description("Total number of users in system")
                .register(meterRegistry);
    }

    // ==================== 计数器方法 ====================

    /**
     * 记录用户注册
     */
    public void recordUserRegistration() {
        userRegistrationCounter.increment();
        log.debug("User registration recorded");
    }

    /**
     * 记录用户登录成功
     */
    public void recordUserLogin(String loginMethod) {
        Counter.builder("user.login.total")
                .tag("method", loginMethod)
                .register(meterRegistry)
                .increment();
        log.debug("User login recorded: method={}", loginMethod);
    }

    /**
     * 记录用户登录失败
     */
    public void recordUserLoginFailure(String reason) {
        Counter.builder("user.login.failure.total")
                .tag("reason", reason)
                .register(meterRegistry)
                .increment();
        log.debug("User login failure recorded: reason={}", reason);
    }

    /**
     * 记录用户资料更新
     */
    public void recordUserProfileUpdate(String updateType) {
        Counter.builder("user.profile.update.total")
                .tag("type", updateType)
                .register(meterRegistry)
                .increment();
        log.debug("User profile update recorded: type={}", updateType);
    }

    /**
     * 记录用户项目操作
     */
    public void recordUserProjectOperation(String operation) {
        Counter.builder("user.project.operation.total")
                .tag("operation", operation)
                .register(meterRegistry)
                .increment();
        log.debug("User project operation recorded: operation={}", operation);
    }

    /**
     * 记录用户工作经历操作
     */
    public void recordUserWorkHistoryOperation(String operation) {
        Counter.builder("user.work.history.operation.total")
                .tag("operation", operation)
                .register(meterRegistry)
                .increment();
        log.debug("User work history operation recorded: operation={}", operation);
    }

    /**
     * 记录用户教育经历操作
     */
    public void recordUserEducationOperation(String operation) {
        Counter.builder("user.education.operation.total")
                .tag("operation", operation)
                .register(meterRegistry)
                .increment();
        log.debug("User education operation recorded: operation={}", operation);
    }

    /**
     * 记录缓存命中
     */
    public void recordCacheHit(String cacheType) {
        Counter.builder("cache.hit.total")
                .tag("type", cacheType)
                .register(meterRegistry)
                .increment();
        log.debug("Cache hit recorded: type={}", cacheType);
    }

    /**
     * 记录缓存未命中
     */
    public void recordCacheMiss(String cacheType) {
        Counter.builder("cache.miss.total")
                .tag("type", cacheType)
                .register(meterRegistry)
                .increment();
        log.debug("Cache miss recorded: type={}", cacheType);
    }

    // ==================== 计时器方法 ====================

    /**
     * 记录数据库查询时间
     */
    public void recordDatabaseQueryTime(Duration duration, String queryType) {
        databaseQueryTimer.record(duration);
        log.debug("Database query time recorded: duration={}ms, type={}", 
                duration.toMillis(), queryType);
    }

    /**
     * 记录Redis操作时间
     */
    public void recordRedisOperationTime(Duration duration, String operation) {
        redisOperationTimer.record(duration);
        log.debug("Redis operation time recorded: duration={}ms, operation={}", 
                duration.toMillis(), operation);
    }

    /**
     * 记录用户认证时间
     */
    public void recordUserAuthTime(Duration duration, String authType) {
        userAuthTimer.record(duration);
        log.debug("User auth time recorded: duration={}ms, type={}", 
                duration.toMillis(), authType);
    }

    // ==================== 实时指标方法 ====================

    /**
     * 增加活跃用户会话数
     */
    public void incrementActiveUserSessions() {
        activeUserSessions.incrementAndGet();
    }

    /**
     * 减少活跃用户会话数
     */
    public void decrementActiveUserSessions() {
        activeUserSessions.decrementAndGet();
    }

    /**
     * 增加待处理操作数
     */
    public void incrementPendingOperations() {
        pendingOperations.incrementAndGet();
    }

    /**
     * 减少待处理操作数
     */
    public void decrementPendingOperations() {
        pendingOperations.decrementAndGet();
    }

    // ==================== Gauge指标计算方法 ====================

    /**
     * 获取活跃数据库连接数
     */
    private double getActiveDatabaseConnections() {
        try (Connection connection = dataSource.getConnection();
             PreparedStatement stmt = connection.prepareStatement(
                     "SELECT COUNT(*) FROM information_schema.processlist WHERE db = DATABASE()")) {
            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                return rs.getDouble(1);
            }
        } catch (Exception e) {
            log.warn("Failed to get active database connections", e);
        }
        return 0;
    }

    /**
     * 获取Redis连接状态
     */
    private double getRedisConnectionStatus() {
        try {
            redisTemplate.opsForValue().get("health_check");
            return 1.0; // 连接正常
        } catch (Exception e) {
            log.warn("Redis connection check failed", e);
            return 0.0; // 连接异常
        }
    }

    /**
     * 计算缓存命中率
     */
    private double getCacheHitRate() {
        double hits = cacheHitCounter.count();
        double misses = cacheMissCounter.count();
        double total = hits + misses;
        return total > 0 ? (hits / total) * 100 : 0;
    }

    /**
     * 获取用户总数
     */
    private double getTotalUserCount() {
        try (Connection connection = dataSource.getConnection();
             PreparedStatement stmt = connection.prepareStatement("SELECT COUNT(*) FROM user_base")) {
            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                return rs.getDouble(1);
            }
        } catch (Exception e) {
            log.warn("Failed to get total user count", e);
        }
        return 0;
    }
}
