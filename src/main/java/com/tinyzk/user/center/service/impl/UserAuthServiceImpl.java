package com.tinyzk.user.center.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tinyzk.user.center.common.annotation.UserAudit;
import com.tinyzk.user.center.common.context.ClientContext;
import com.tinyzk.user.center.common.enums.IdentityType;
import com.tinyzk.user.center.common.enums.OperationType;
import com.tinyzk.user.center.common.event.UserAuditEvent;
import com.tinyzk.user.center.common.exception.BusinessException;
import com.tinyzk.user.center.common.exception.ErrorCode;
import com.tinyzk.user.center.dto.LoginDTO;
import com.tinyzk.user.center.dto.RegisterDTO;
import com.tinyzk.user.center.entity.UserAuth;
import com.tinyzk.user.center.entity.UserBase;
import com.tinyzk.user.center.entity.UserProfile;
import com.tinyzk.user.center.mapper.UserAuthMapper;
import com.tinyzk.user.center.mapper.UserBaseMapper;
import com.tinyzk.user.center.mapper.UserProfileMapper;
import com.tinyzk.user.center.service.UserAuthService;
import com.tinyzk.user.center.service.UserExternalMappingService;
import com.tinyzk.user.center.common.util.JwtUtil;
import com.tinyzk.user.center.vo.LoginVO;
import com.tinyzk.user.center.vo.RegisterVO;
import com.tinyzk.user.center.common.exception.ClientValidationException;

import cn.hutool.core.bean.BeanUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import java.time.LocalDateTime;


/**
 * 用户认证服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserAuthServiceImpl implements UserAuthService {
    
    private final UserAuthMapper userAuthMapper;
    private final UserBaseMapper userBaseMapper;
    private final UserProfileMapper userProfileMapper;
    private final UserExternalMappingService userExternalMappingService;
    private final PasswordEncoder passwordEncoder;
    private final ApplicationEventPublisher eventPublisher;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    @UserAudit(type = OperationType.LOGIN_SUCCESS, failType = OperationType.LOGIN_FAIL, detail = "用户登录")
    @Caching(evict = {
        @CacheEvict(value = "userListCache", allEntries = true),
        @CacheEvict(value = "userDetailCache", key = "#result.userId", condition = "#result != null")
    })
    public LoginVO login(LoginDTO loginDTO) {
        log.info("用户登录: identityType={}, identifier={}", loginDTO.getIdentityType(), loginDTO.getIdentifier());
        
        // 查询用户认证信息
        UserAuth userAuth = getUserAuth(loginDTO.getIdentityType(), loginDTO.getIdentifier());
        
        // 如果未找到用户认证信息，说明是新用户，进行隐式注册
        if (userAuth == null) {
            log.info("用户首次登录，进行隐式注册: identityType={}, identifier={}", loginDTO.getIdentityType(), loginDTO.getIdentifier());
            
            // 创建注册DTO
            RegisterDTO registerDTO = new RegisterDTO();
            registerDTO.setIdentityType(loginDTO.getIdentityType());
            registerDTO.setIdentifier(loginDTO.getIdentifier());
            registerDTO.setCredential(loginDTO.getCredential());
            
            // 调用注册方法
            RegisterVO registerVO = register(registerDTO);
            LoginVO loginVO = new LoginVO();
            BeanUtil.copyProperties(registerVO, loginVO);
            loginVO.setToken(JwtUtil.generateToken(registerVO.getUserId()));
            return loginVO;
        }
        
        // 验证用户状态
        UserBase userBase = userBaseMapper.selectById(userAuth.getUserId());
        if (userBase == null || userBase.getStatus() != 1) {
            log.warn("用户状态异常: userId={}, status={}", userAuth.getUserId(), userBase != null ? userBase.getStatus() : "null");
            
            // 发布审计事件
            publishAuditEvent(userAuth.getUserId(), OperationType.LOGIN_FAIL, "用户状态异常");
            
            throw new BusinessException(ErrorCode.USER_STATUS_ERROR);
        }
        
        // 如果是需要验证凭证的登录方式（如密码登录），则验证凭证
        if (needCredentialVerification(loginDTO.getIdentityType()) && userAuth.getCredential() != null) {
            if (loginDTO.getCredential() == null || !passwordEncoder.matches(loginDTO.getCredential(), userAuth.getCredential())) {
                log.warn("凭证验证失败: userId={}", userAuth.getUserId());
                
                // 发布审计事件
                publishAuditEvent(userAuth.getUserId(), OperationType.LOGIN_FAIL, "凭证验证失败");
                
                throw new BusinessException(ErrorCode.CREDENTIAL_VERIFICATION_FAILED);
            }
        }
        
        // 更新最后登录时间
        userAuth.setLastLoginAt(LocalDateTime.now());
        userAuthMapper.updateById(userAuth);
        
        // 发布审计事件
        publishAuditEvent(userAuth.getUserId(), OperationType.LOGIN_SUCCESS, "登录成功");
        
        // 查询用户资料
        UserProfile userProfile = getUserProfile(userAuth.getUserId());
        
        // 构建登录响应
        LoginVO loginVO = new LoginVO();
        loginVO.setUserId(userAuth.getUserId());
        loginVO.setIsNewUser(false);
        loginVO.setRealNameVerified(userBase.getRealNameVerified());
        
        if (userProfile != null) {
            loginVO.setNickname(userProfile.getNickname());
            loginVO.setAvatarUrl(userProfile.getAvatarUrl());
        }
        loginVO.setToken(JwtUtil.generateToken(userAuth.getUserId()));
        return loginVO;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    @UserAudit(type = OperationType.REGISTER_SUCCESS, failType = OperationType.REGISTER_FAIL, detail = "用户注册")
    @Caching(evict = {
        @CacheEvict(value = "userListCache", allEntries = true),
        @CacheEvict(value = "userDetailCache", key = "#result.userId", condition = "#result != null")
    })
    public RegisterVO register(RegisterDTO registerDTO) {
        log.info("用户注册: identityType={}, identifier={}", registerDTO.getIdentityType(), registerDTO.getIdentifier());
        
        // 检查用户是否已存在
        UserAuth existingAuth = getUserAuth(registerDTO.getIdentityType(), registerDTO.getIdentifier());
        if (existingAuth != null) {
            log.warn("用户已存在: identityType={}, identifier={}", registerDTO.getIdentityType(), registerDTO.getIdentifier());
            throw new BusinessException(ErrorCode.USER_ALREADY_EXISTS);
        }
        
        // 获取当前客户端信息
        String currentClientId = ClientContext.getCurrentClientId();
        if (ClientContext.isAnonymous()) {
            log.warn("匿名客户端尝试注册用户: identityType={}, identifier={}", registerDTO.getIdentityType(), registerDTO.getIdentifier());
            throw new ClientValidationException("注册用户需要有效的客户端身份，请在请求头中添加 X-Client-ID 参数");
        }
        
        // 创建用户基础信息
        UserBase userBase = new UserBase();
        userBase.setRealNameVerified(false);
        userBase.setStatus(1); // 正常状态
        userBaseMapper.insert(userBase);
        
        Long userId = userBase.getUserId();
        log.info("创建用户基础信息成功: userId={}", userId);
        
        // 创建用户认证信息
        UserAuth userAuth = new UserAuth();
        userAuth.setUserId(userId);
        userAuth.setIdentityType(registerDTO.getIdentityType().name());
        userAuth.setIdentifier(registerDTO.getIdentifier());
        
        // 如果是需要凭证的登录方式，则加密凭证
        if (needCredentialVerification(registerDTO.getIdentityType()) && registerDTO.getCredential() != null) {
            userAuth.setCredential(passwordEncoder.encode(registerDTO.getCredential()));
        }
        
        userAuth.setVerified(1);
        userAuth.setLastLoginAt(LocalDateTime.now());
        userAuthMapper.insert(userAuth);
        
        log.info("创建用户认证信息成功: userId={}, authId={}", userId, userAuth.getAuthId());

        // 创建用户外部映射关系
        try {
            String externalUserId = registerDTO.getExternalUserId() != null ? 
                                 registerDTO.getExternalUserId() : 
                                 registerDTO.getIdentifier(); // 如果提供了外部ID则使用，否则使用identifier
                
            String metadata = String.format("{\"identityType\":\"%s\",\"registeredAt\":\"%s\",\"clientId\":\"%s\",\"externalUserId\":\"%s\"}", 
                                          registerDTO.getIdentityType().name(), 
                                          LocalDateTime.now(),
                                          currentClientId,
                                          externalUserId);
            
            // 只有当externalUserId不为空时才创建映射
            if (externalUserId != null && !externalUserId.trim().isEmpty()) {
                userExternalMappingService.createMapping(userId, externalUserId, currentClientId, metadata);
                log.info("创建用户外部映射关系成功: userId={}, externalUserId={}, clientId={}", 
                         userId, externalUserId, currentClientId);
            } else {
                log.warn("未提供有效的外部用户ID，跳过创建映射关系: userId={}, clientId={}", userId, currentClientId);
            }
                     
        } catch (Exception e) {
            log.error("创建用户外部映射关系失败: userId={}, clientId={}", userId, currentClientId, e);
            // 这里不抛出异常，避免影响注册流程，但需要记录日志用于后续处理
            // 可以考虑异步补偿机制
        }

        // 创建用户资料
        if (registerDTO.getNickname() != null || registerDTO.getAvatarUrl() != null) {
            UserProfile userProfile = new UserProfile();
            userProfile.setUserId(userId);
            userProfile.setNickname(registerDTO.getNickname());
            userProfile.setAvatarUrl(registerDTO.getAvatarUrl());
            userProfileMapper.insert(userProfile);
        }
        
        // 构建登录响应
        RegisterVO registerVO = new RegisterVO();
        registerVO.setUserId(userId);
        registerVO.setIsNewUser(true);
        registerVO.setRealNameVerified(false);
        registerVO.setNickname(registerDTO.getNickname());
        registerVO.setAvatarUrl(registerDTO.getAvatarUrl());
        return registerVO;
    }
    
    /**
     * 获取用户认证信息
     *
     * @param identityType 身份类型
     * @param identifier  身份标识
     * @return 用户认证信息
     */
    private UserAuth getUserAuth(IdentityType identityType, String identifier) {
        LambdaQueryWrapper<UserAuth> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserAuth::getIdentityType, identityType.name())
                .eq(UserAuth::getIdentifier, identifier)
                .isNull(UserAuth::getDeletedAt);
        
        return userAuthMapper.selectOne(queryWrapper);
    }
    
    /**
     * 获取用户资料
     *
     * @param userId 用户ID
     * @return 用户资料
     */
    private UserProfile getUserProfile(Long userId) {
        LambdaQueryWrapper<UserProfile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserProfile::getUserId, userId)
                .isNull(UserProfile::getDeletedAt);
        
        return userProfileMapper.selectOne(queryWrapper);
    }
    
    /**
     * 判断是否需要验证凭证
     *
     * @param identityType 身份类型
     * @return 是否需要验证凭证
     */
    private boolean needCredentialVerification(IdentityType identityType) {
        return identityType == IdentityType.USERNAME ||
               identityType == IdentityType.EMAIL;
    }
    
    /**
     * 发布审计事件
     *
     * @param userId        用户ID
     * @param operationType 操作类型
     * @param detail        操作详情
     */
    private void publishAuditEvent(Long userId, OperationType operationType, String detail) {
        // 获取当前客户端信息
        String currentClientId = ClientContext.getCurrentClientId();
        
        UserAuditEvent event = UserAuditEvent.builder()
                .userId(userId)
                .operationType(operationType)
                .detail(detail)
                .clientId(currentClientId)  // 添加客户端ID
                .build();
        
        eventPublisher.publishEvent(event);
        log.debug("发布审计事件: userId={}, operationType={}, clientId={}", 
                  userId, operationType, currentClientId);
    }
}
