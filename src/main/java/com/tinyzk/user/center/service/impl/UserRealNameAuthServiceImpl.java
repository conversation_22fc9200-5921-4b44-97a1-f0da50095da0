package com.tinyzk.user.center.service.impl;

import cn.hutool.core.util.DesensitizedUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.tinyzk.user.center.common.annotation.UserAudit;
import com.tinyzk.user.center.common.enums.OperationType;
import com.tinyzk.user.center.common.enums.RealNameAuthStatus;
import com.tinyzk.user.center.common.enums.UserStatus;
import com.tinyzk.user.center.common.event.UserAuditEvent;
import com.tinyzk.user.center.common.exception.BusinessException;
import com.tinyzk.user.center.common.exception.ErrorCode;
import com.tinyzk.user.center.dto.RealNameAuthDTO;
import com.tinyzk.user.center.entity.*;
import com.tinyzk.user.center.mapper.*;
import com.tinyzk.user.center.service.UserRealNameAuthService;
import com.tinyzk.user.center.util.AESEncryptUtil;
import com.tinyzk.user.center.vo.RealNameAuthVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;

/**
 * 用户实名认证服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserRealNameAuthServiceImpl implements UserRealNameAuthService {

    private final UserRealNameAuthMapper userRealNameAuthMapper;
    private final UserBaseMapper userBaseMapper;
    private final UserAuthMapper userAuthMapper;
    private final UserProfileMapper userProfileMapper;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @UserAudit(type = OperationType.REALNAME_AUTH_SUBMIT, failType = OperationType.REALNAME_AUTH_FAIL, detail = "提交实名认证")
    @Caching(evict = {
        @CacheEvict(value = "userDetailCache", key = "#realNameAuthDTO.userId")
    })
    public RealNameAuthVO submit(RealNameAuthDTO realNameAuthDTO) {
        Long userId = realNameAuthDTO.getUserId();
        log.info("用户提交实名认证: userId={}", userId);

        // 1. 检查用户是否存在
        UserBase userBase = userBaseMapper.selectById(userId);
        if (userBase == null) {
            log.warn("提交实名认证失败，用户不存在: userId={}", userId);
            throw new BusinessException(ErrorCode.USER_NOT_EXISTS);
        }

        // 2. 检查是否已存在认证记录
        UserRealNameAuth existingAuth = getAuthByUserId(userId);
        if (existingAuth != null && existingAuth.getAuthStatus() == RealNameAuthStatus.VERIFIED.getCode()) {
            log.warn("用户已完成实名认证，无需重复提交: userId={}", userId);
            return mapToVO(existingAuth);
        } else if (existingAuth != null && existingAuth.getAuthStatus() == RealNameAuthStatus.PENDING.getCode()) {
            log.warn("用户实名认证正在审核中，请勿重复提交: userId={}", userId);
            throw new BusinessException(ErrorCode.BUSINESS_ERROR, "实名认证正在审核中，请勿重复提交");
        }

        // 3. 加密身份证号和姓名
        String encryptedIdNumber = encrypt(realNameAuthDTO.getIdNumber(), userId);
        String encryptedRealName = encrypt(realNameAuthDTO.getRealName(), userId);

        // 4. 创建或更新认证记录
        UserRealNameAuth realNameAuth = createOrUpdateAuthRecord(existingAuth, userId, realNameAuthDTO.getRealName(), encryptedIdNumber);

        // 5. 执行账户合并逻辑
        RealNameAuthVO result = processAccountMerge(userId, encryptedRealName, encryptedIdNumber);
        if (result != null) {
            return result;
        }

        //  没有需要合并的账户
        // 6. 更新用户基础表的认证状态
        updateUserBaseForAuth(userBase, encryptedRealName, encryptedIdNumber, userId);

        // 7. 发布实名认证成功事件
        publishAuditEvent(
            userId,
            OperationType.REALNAME_AUTH_SUCCESS,
            String.format("用户[%s]完成实名认证", userId)
        );

        // 8. 转换并返回VO (执行脱敏)
        return mapToVO(realNameAuth);
    }

    // 加密
    private String encrypt(String charString, Long userId) {
        try {
            return AESEncryptUtil.encrypt(charString);
        } catch (Exception e) {
            log.error("身份信息加密失败: userId={}", userId, e);
            throw new BusinessException(ErrorCode.SYSTEM_ERROR, "身份信息加密失败");
        }
    }

    // 创建或更新认证记录
    private UserRealNameAuth createOrUpdateAuthRecord(UserRealNameAuth existingAuth, Long userId, String realName, String encryptedIdNumber) {
        UserRealNameAuth realNameAuth = existingAuth != null ? existingAuth : new UserRealNameAuth();
        realNameAuth.setUserId(userId);
        realNameAuth.setSubmittedRealName(realName);
        realNameAuth.setSubmittedIdCardNumber(encryptedIdNumber);
        realNameAuth.setAuthStatus(RealNameAuthStatus.VERIFIED.getCode());
        realNameAuth.setAuthTime(LocalDateTime.now());
        if (existingAuth == null) {
            userRealNameAuthMapper.insert(realNameAuth);
            log.info("创建实名认证记录成功: userId={}, authId={}", userId, realNameAuth.getAuthRecordId());
        } else {
            userRealNameAuthMapper.updateById(realNameAuth);
            log.info("更新实名认证记录成功: userId={}, authId={}", userId, realNameAuth.getAuthRecordId());
        }
        return realNameAuth;
    }

    // 更新用户基础表的认证状态
    private void updateUserBaseForAuth(UserBase userBase, String encryptedRealName, String encryptedIdNumber, Long userId) {
        userBase.setRealNameVerified(true);
        userBase.setRealName(encryptedRealName);
        userBase.setIdCardNumber(encryptedIdNumber);
        userBaseMapper.updateById(userBase);
        log.info("更新用户基础表实名认证状态成功: userId={}", userId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Caching(evict = {
        @CacheEvict(value = "userDetailCache", key = "#sourceUserId")
    })
    public RealNameAuthVO processAccountMerge(Long sourceUserId, String encryptedRealName, String encryptedIdNumber) {
        log.info("执行账户合并逻辑检查: sourceUserId={}", sourceUserId);
        List<UserBase> matchedUsers = findMatchedUsersForMerge(sourceUserId, encryptedRealName, encryptedIdNumber);
        if (matchedUsers == null || matchedUsers.isEmpty()) {
            log.info("未发现需要合并的账户: sourceUserId={}", sourceUserId);
            return null;
        }
        UserBase targetUser = selectTargetUserForMerge(matchedUsers);
        Long targetUserId = targetUser.getUserId();
        log.info("开始执行账户合并: sourceUserId={}, targetUserId={}", sourceUserId, targetUserId);
        try {
            publishAccountMergeStartEvent(sourceUserId, targetUserId);
            transferUserAuth(sourceUserId, targetUserId);
            handleUserProfile(sourceUserId, targetUserId);
            updateSourceUserStatus(sourceUserId, targetUserId);
            ensureTargetUserVerified(targetUserId);
            UserRealNameAuth targetAuth = getAuthByUserId(targetUserId);
            if (targetAuth != null) {
                log.info("账户合并完成，返回目标账户认证信息: sourceUserId={}, targetUserId={}", sourceUserId, targetUserId);
                return mapToVO(targetAuth);
            }
            return null;
        } catch (Exception e) {
            log.error("账户合并过程发生异常: sourceUserId={}, targetUserId={}", sourceUserId, targetUserId, e);
            throw new BusinessException(ErrorCode.SYSTEM_ERROR, "账户合并失败");
        }
    }

    // 查找需要合并的用户
    private List<UserBase> findMatchedUsersForMerge(Long sourceUserId, String encryptedRealName, String encryptedIdNumber) {
        LambdaQueryWrapper<UserBase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserBase::getRealName, encryptedRealName)
                   .eq(UserBase::getIdCardNumber, encryptedIdNumber)
                   .eq(UserBase::getStatus, UserStatus.NORMAL.getCode())
                   .eq(UserBase::getRealNameVerified, true)
                   .ne(UserBase::getUserId, sourceUserId)
                   .isNull(UserBase::getDeletedAt);
        return userBaseMapper.selectList(queryWrapper);
    }

    // 选择目标用户
    private UserBase selectTargetUserForMerge(List<UserBase> matchedUsers) {
        return matchedUsers.stream().min(Comparator.comparing(UserBase::getCreatedAt)).orElse(null);
    }

    // 发布账户合并开始事件
    private void publishAccountMergeStartEvent(Long sourceUserId, Long targetUserId) {
        Map<String, Object> startDetails = new HashMap<>();
        startDetails.put("targetUserId", targetUserId);
        startDetails.put("sourceUserId", sourceUserId);
        publishAuditEvent(
            sourceUserId,
            OperationType.ACCOUNT_MERGE_START,
            JSON.toJSONString(startDetails)
        );
    }

    /**
     * 转移用户认证方式
     *
     * @param sourceUserId 源用户ID
     * @param targetUserId 目标用户ID
     */
    private void transferUserAuth(Long sourceUserId, Long targetUserId) {
        // 1. 查询源用户的所有有效认证方式
        LambdaQueryWrapper<UserAuth> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserAuth::getUserId, sourceUserId)
                   .isNull(UserAuth::getDeletedAt);

        List<UserAuth> sourceAuthList = userAuthMapper.selectList(queryWrapper);

        if (sourceAuthList.isEmpty()) {
            log.info("源用户没有有效的认证方式: sourceUserId={}", sourceUserId);
            return;
        }

        // 2. 获取源用户认证ID列表(用于日志记录)
        List<Long> sourceAuthIds = sourceAuthList.stream()
                .map(UserAuth::getAuthId)
                .collect(Collectors.toList());

        // 3. 更新认证方式的用户ID
        LambdaUpdateWrapper<UserAuth> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UserAuth::getUserId, sourceUserId)
                    .isNull(UserAuth::getDeletedAt)
                    .set(UserAuth::getUserId, targetUserId);

        int updatedCount = userAuthMapper.update(null, updateWrapper);
        log.info("转移用户认证方式完成: sourceUserId={}, targetUserId={}, updatedCount={}",
                sourceUserId, targetUserId, updatedCount);

        // 4. 发布认证方式转移事件
        Map<String, Object> authTransferDetails = new HashMap<>();
        authTransferDetails.put("sourceAuthIds", sourceAuthIds);
        authTransferDetails.put("sourceUserId", sourceUserId);
        authTransferDetails.put("targetUserId", targetUserId);
        authTransferDetails.put("updatedCount", updatedCount);

        publishAuditEvent(
            sourceUserId,
            OperationType.ACCOUNT_MERGE_AUTH_TRANSFER,
            JSON.toJSONString(authTransferDetails)
        );
    }

    /**
     * 处理用户资料 (采用保留目标账户信息的策略)
     *
     * @param sourceUserId 源用户ID
     * @param targetUserId 目标用户ID
     */
    private void handleUserProfile(Long sourceUserId, Long targetUserId) {
        // 1. 查询源用户的资料
        LambdaQueryWrapper<UserProfile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserProfile::getUserId, sourceUserId)
                   .isNull(UserProfile::getDeletedAt);

        UserProfile sourceProfile = userProfileMapper.selectOne(queryWrapper);

        if (sourceProfile == null) {
            log.info("源用户没有资料信息: sourceUserId={}", sourceUserId);
            return;
        }

        // 2. 发布资料处理事件 (不合并Profile，仅记录日志)
        Map<String, Object> profileDetails = new HashMap<>();
        profileDetails.put("strategy", "retain_target");
        profileDetails.put("sourceProfile", JSONObject.toJSONString(sourceProfile));
        profileDetails.put("sourceUserId", sourceUserId);
        profileDetails.put("targetUserId", targetUserId);

        publishAuditEvent(
            sourceUserId,
            OperationType.ACCOUNT_MERGE_PROFILE_UPDATE,
            JSON.toJSONString(profileDetails)
        );

        // 3. 逻辑删除源用户资料 (可选)
        sourceProfile.setDeletedAt(LocalDateTime.now());
        userProfileMapper.updateById(sourceProfile);
        log.info("标记源用户资料为已删除: sourceUserId={}", sourceUserId);
    }

    /**
     * 更新源用户状态为已合并
     *
     * @param sourceUserId 源用户ID
     * @param targetUserId 目标用户ID
     */
    private void updateSourceUserStatus(Long sourceUserId, Long targetUserId) {
        // 1. 查询源用户
        UserBase sourceUser = userBaseMapper.selectById(sourceUserId);
        if (sourceUser == null) {
            log.warn("未找到源用户: sourceUserId={}", sourceUserId);
            return;
        }

        // 2. 记录源用户之前的状态 (用于日志记录)
        Integer oldStatus = sourceUser.getStatus();

        // 3. 更新源用户状态为已合并并设置逻辑删除时间
        sourceUser.setStatus(UserStatus.MERGED.getCode());
        sourceUser.setDeletedAt(LocalDateTime.now());
        userBaseMapper.updateById(sourceUser);
        log.info("更新源用户状态为已合并: sourceUserId={}, oldStatus={}, newStatus={}",
                sourceUserId, oldStatus, UserStatus.MERGED.getCode());

        // 4. 发布状态变更事件
        Map<String, Object> statusDetails = new HashMap<>();
        statusDetails.put("mergedIntoUserId", targetUserId);
        statusDetails.put("sourceUserId", sourceUserId);
        statusDetails.put("oldStatus", oldStatus);
        statusDetails.put("newStatus", UserStatus.MERGED.getCode());

        publishAuditEvent(
            sourceUserId,
            OperationType.ACCOUNT_MERGE_STATUS_CHANGE,
            JSON.toJSONString(statusDetails)
        );
    }

    /**
     * 确保目标用户为已实名认证状态
     *
     * @param targetUserId 目标用户ID
     */
    private void ensureTargetUserVerified(Long targetUserId) {
        UserBase targetUser = userBaseMapper.selectById(targetUserId);
        if (targetUser != null && !targetUser.getRealNameVerified()) {
            targetUser.setRealNameVerified(true);
            userBaseMapper.updateById(targetUser);
            log.info("更新目标用户实名认证状态: targetUserId={}", targetUserId);
        }
    }

    /**
     * 发布用户审计事件
     *
     * @param userId 用户ID
     * @param operationType 操作类型
     * @param detail 详细信息
     */
    private void publishAuditEvent(Long userId, OperationType operationType, String detail) {
        try {
            UserAuditEvent event = UserAuditEvent.builder()
                    .userId(userId)
                    .operationType(operationType)
                    .detail(detail)
                    .build();

            eventPublisher.publishEvent(event);
            log.debug("发布用户审计事件成功: userId={}, operationType={}", userId, operationType.name());
        } catch (Exception e) {
            // 事件发布失败不应影响主流程
            log.error("发布用户审计事件失败: userId={}, operationType={}", userId, operationType.name(), e);
        }
    }

    @Override
    public RealNameAuthVO getByUserId(Long userId) {
        log.debug("查询用户实名认证信息: userId={}", userId);
        UserRealNameAuth realNameAuth = getAuthByUserId(userId);
        if (realNameAuth == null) {
            log.warn("未找到用户的实名认证记录: userId={}", userId);
            throw new BusinessException(ErrorCode.REAL_NAME_AUTH_NOT_FOUND);
        }
        return mapToVO(realNameAuth);
    }

    // --- Helper Methods ---

    private UserRealNameAuth getAuthByUserId(Long userId) {
        LambdaQueryWrapper<UserRealNameAuth> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserRealNameAuth::getUserId, userId);
        return userRealNameAuthMapper.selectOne(queryWrapper);
    }

    private RealNameAuthVO mapToVO(UserRealNameAuth entity) {
        RealNameAuthVO vo = new RealNameAuthVO();
        BeanUtils.copyProperties(entity, vo);

        // 脱敏处理
        vo.setRealName(maskRealName(entity.getSubmittedRealName()));
        try {
            vo.setIdNumber(maskIdNumber(AESEncryptUtil.decrypt(entity.getSubmittedIdCardNumber())));
        } catch (Exception e) {
            log.error("身份证号解密失败，无法脱敏: authId={}", entity.getAuthRecordId(), e);
            vo.setIdNumber("******"); // 解密失败则显示星号
        }

        return vo;
    }

    private String maskRealName(String realName) {
        return DesensitizedUtil.chineseName(realName);
    }

    private String maskIdNumber(String idNumber) {
        return DesensitizedUtil.idCardNum(idNumber, 1, 2);
    }
}
