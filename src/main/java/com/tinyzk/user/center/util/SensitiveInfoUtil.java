package com.tinyzk.user.center.util;

/**
 * 敏感信息处理工具类
 */
public class SensitiveInfoUtil {
    
    /**
     * 对邮箱地址进行脱敏处理
     * 规则：保留前3个字符和@后面的内容，中间用*代替
     * 例如：<EMAIL> -> use***@example.com
     * 
     * @param email 邮箱地址
     * @return 脱敏后的邮箱地址
     */
    public static String maskEmail(String email) {
        if (email == null || email.isEmpty()) {
            return email;
        }
        
        int atIndex = email.indexOf('@');
        if (atIndex <= 0) {
            return email;
        }
        
        String prefix = email.substring(0, atIndex);
        String suffix = email.substring(atIndex);
        
        if (prefix.length() <= 3) {
            return prefix + "***" + suffix;
        } else {
            return prefix.substring(0, 3) + "***" + suffix;
        }
    }
    
    /**
     * 对手机号进行脱敏处理
     * 规则：保留前3位和后4位，中间用*代替
     * 例如：13800138000 -> 138****8000
     * 
     * @param phone 手机号
     * @return 脱敏后的手机号
     */
    public static String maskPhone(String phone) {
        if (phone == null || phone.isEmpty()) {
            return phone;
        }
        
        if (phone.length() <= 7) {
            return phone;
        }
        
        return phone.substring(0, 3) + "****" + phone.substring(phone.length() - 4);
    }
    
    /**
     * 根据身份类型对标识进行脱敏处理
     * 
     * @param identityType 身份类型
     * @param identifier 标识
     * @return 脱敏后的标识
     */
    public static String maskIdentifier(String identityType, String identifier) {
        if (identifier == null || identifier.isEmpty()) {
            return identifier;
        }
        
        if ("EMAIL".equals(identityType)) {
            return maskEmail(identifier);
        } else if ("PHONE".equals(identityType)) {
            return maskPhone(identifier);
        } else {
            // 对于其他类型的标识，保留前后各1/4的字符，中间用*代替
            int length = identifier.length();
            int prefixLength = Math.max(1, length / 4);
            int suffixLength = Math.max(1, length / 4);
            
            if (length <= prefixLength + suffixLength) {
                return identifier;
            }
            
            String prefix = identifier.substring(0, prefixLength);
            String suffix = identifier.substring(length - suffixLength);
            
            return prefix + "****" + suffix;
        }
    }
}
