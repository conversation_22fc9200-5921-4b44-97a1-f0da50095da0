package com.tinyzk.user.center.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 自定义健康检查指示器
 * 检查数据库、Redis、Nacos等关键组件的健康状态
 */
@Slf4j
@Component
public class CustomHealthIndicator implements HealthIndicator {

    private final DataSource dataSource;
    private final RedisTemplate<String, Object> redisTemplate;

    public CustomHealthIndicator(DataSource dataSource, 
                               RedisTemplate<String, Object> redisTemplate) {
        this.dataSource = dataSource;
        this.redisTemplate = redisTemplate;
    }

    @Override
    public Health health() {
        Map<String, Object> details = new HashMap<>();
        boolean allHealthy = true;

        // 检查数据库健康状态
        Health.Builder databaseHealth = checkDatabaseHealth();
        details.put("database", databaseHealth.build().getDetails());
        if (databaseHealth.build().getStatus().getCode().equals("DOWN")) {
            allHealthy = false;
        }

        // 检查Redis健康状态
        Health.Builder redisHealth = checkRedisHealth();
        details.put("redis", redisHealth.build().getDetails());
        if (redisHealth.build().getStatus().getCode().equals("DOWN")) {
            allHealthy = false;
        }

        // 检查业务逻辑健康状态
        Health.Builder businessHealth = checkBusinessHealth();
        details.put("business", businessHealth.build().getDetails());
        if (businessHealth.build().getStatus().getCode().equals("DOWN")) {
            allHealthy = false;
        }

        // 添加系统信息
        details.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        details.put("application", "user-center");

        return allHealthy ? 
            Health.up().withDetails(details).build() : 
            Health.down().withDetails(details).build();
    }

    /**
     * 检查数据库健康状态
     */
    private Health.Builder checkDatabaseHealth() {
        try (Connection connection = dataSource.getConnection()) {
            // 检查数据库连接
            if (connection.isValid(5)) {
                // 执行简单查询验证数据库功能
                try (PreparedStatement stmt = connection.prepareStatement("SELECT 1")) {
                    ResultSet rs = stmt.executeQuery();
                    if (rs.next() && rs.getInt(1) == 1) {
                        // 获取数据库详细信息
                        Map<String, Object> dbDetails = getDatabaseDetails(connection);
                        return Health.up()
                            .withDetail("status", "UP")
                            .withDetail("database", dbDetails.get("database"))
                            .withDetail("version", dbDetails.get("version"))
                            .withDetail("connection_count", dbDetails.get("connection_count"))
                            .withDetail("uptime", dbDetails.get("uptime"));
                    }
                }
            }
        } catch (Exception e) {
            log.error("Database health check failed", e);
            return Health.down()
                .withDetail("status", "DOWN")
                .withDetail("error", e.getMessage())
                .withDetail("error_type", e.getClass().getSimpleName());
        }

        return Health.down()
            .withDetail("status", "DOWN")
            .withDetail("error", "Database connection validation failed");
    }

    /**
     * 检查Redis健康状态
     */
    private Health.Builder checkRedisHealth() {
        try {
            // 测试基本的读写操作
            String testKey = "health_check_" + System.currentTimeMillis();
            String testValue = "test_value";

            redisTemplate.opsForValue().set(testKey, testValue);
            Object retrievedValue = redisTemplate.opsForValue().get(testKey);
            redisTemplate.delete(testKey);

            if (testValue.equals(retrievedValue)) {
                // 获取Redis详细信息
                Map<String, Object> redisDetails = getRedisDetails();
                return Health.up()
                    .withDetail("status", "UP")
                    .withDetail("read_write_test", "PASSED")
                    .withDetail("version", redisDetails.get("version"))
                    .withDetail("memory_usage", redisDetails.get("memory_usage"))
                    .withDetail("connected_clients", redisDetails.get("connected_clients"));
            }
        } catch (Exception e) {
            log.error("Redis health check failed", e);
            return Health.down()
                .withDetail("status", "DOWN")
                .withDetail("error", e.getMessage())
                .withDetail("error_type", e.getClass().getSimpleName());
        }

        return Health.down()
            .withDetail("status", "DOWN")
            .withDetail("error", "Redis read/write test failed");
    }

    /**
     * 检查业务逻辑健康状态
     */
    private Health.Builder checkBusinessHealth() {
        try {
            Map<String, Object> businessDetails = new HashMap<>();

            // 检查关键业务表是否存在且可访问
            boolean userTableHealthy = checkUserTableHealth();
            businessDetails.put("user_table_accessible", userTableHealthy);

            // 检查缓存预热状态
            boolean cacheWarmupHealthy = checkCacheWarmupHealth();
            businessDetails.put("cache_warmup_status", cacheWarmupHealthy ? "HEALTHY" : "DEGRADED");

            // 检查关键配置
            boolean configHealthy = checkConfigurationHealth();
            businessDetails.put("configuration_status", configHealthy ? "HEALTHY" : "DEGRADED");

            // 所有业务检查都通过才认为健康
            if (userTableHealthy && cacheWarmupHealthy && configHealthy) {
                return Health.up()
                    .withDetail("status", "UP")
                    .withDetails(businessDetails);
            } else {
                return Health.down()
                    .withDetail("status", "DEGRADED")
                    .withDetail("message", "Some business components are not healthy")
                    .withDetails(businessDetails);
            }

        } catch (Exception e) {
            log.error("Business health check failed", e);
            return Health.down()
                .withDetail("status", "DOWN")
                .withDetail("error", e.getMessage())
                .withDetail("error_type", e.getClass().getSimpleName());
        }
    }

    /**
     * 获取数据库详细信息
     */
    private Map<String, Object> getDatabaseDetails(Connection connection) {
        Map<String, Object> details = new HashMap<>();
        try {
            // 获取数据库版本
            details.put("database", connection.getMetaData().getDatabaseProductName());
            details.put("version", connection.getMetaData().getDatabaseProductVersion());

            // 获取连接数
            try (PreparedStatement stmt = connection.prepareStatement(
                    "SELECT COUNT(*) FROM information_schema.processlist")) {
                ResultSet rs = stmt.executeQuery();
                if (rs.next()) {
                    details.put("connection_count", rs.getInt(1));
                }
            }

            // 获取数据库运行时间
            try (PreparedStatement stmt = connection.prepareStatement(
                    "SHOW STATUS LIKE 'Uptime'")) {
                ResultSet rs = stmt.executeQuery();
                if (rs.next()) {
                    details.put("uptime", rs.getString("Value") + " seconds");
                }
            }

        } catch (Exception e) {
            log.warn("Failed to get database details", e);
            details.put("details_error", e.getMessage());
        }
        return details;
    }

    /**
     * 获取Redis详细信息
     */
    private Map<String, Object> getRedisDetails() {
        Map<String, Object> details = new HashMap<>();
        try {
            // 这里可以通过Redis INFO命令获取详细信息
            // 由于RedisTemplate的限制，这里提供基本信息
            details.put("version", "Unknown");
            details.put("memory_usage", "Unknown");
            details.put("connected_clients", "Unknown");
        } catch (Exception e) {
            log.warn("Failed to get Redis details", e);
            details.put("details_error", e.getMessage());
        }
        return details;
    }

    /**
     * 检查用户表健康状态
     */
    private boolean checkUserTableHealth() {
        try (Connection connection = dataSource.getConnection();
             PreparedStatement stmt = connection.prepareStatement(
                     "SELECT COUNT(*) FROM user_base LIMIT 1")) {
            ResultSet rs = stmt.executeQuery();
            return rs.next();
        } catch (Exception e) {
            log.warn("User table health check failed", e);
            return false;
        }
    }

    /**
     * 检查缓存预热健康状态
     */
    private boolean checkCacheWarmupHealth() {
        try {
            // 检查缓存预热相关的Redis键是否存在
            Boolean exists = redisTemplate.hasKey("cache:warmup:status");
            return exists != null && exists;
        } catch (Exception e) {
            log.warn("Cache warmup health check failed", e);
            return false;
        }
    }

    /**
     * 检查配置健康状态
     */
    private boolean checkConfigurationHealth() {
        try {
            // 检查关键配置是否正确加载
            // 这里可以检查Spring配置、环境变量等
            return true; // 简化实现
        } catch (Exception e) {
            log.warn("Configuration health check failed", e);
            return false;
        }
    }
}
