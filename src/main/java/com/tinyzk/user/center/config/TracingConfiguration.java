package com.tinyzk.user.center.config;

import io.micrometer.common.KeyValue;
import io.micrometer.observation.ObservationRegistry;
import io.micrometer.observation.aop.ObservedAspect;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.autoconfigure.tracing.zipkin.ZipkinAutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * 链路追踪配置
 * 配置分布式追踪和观测功能
 */
@Slf4j
@Configuration
@Import(ZipkinAutoConfiguration.class)
public class TracingConfiguration {

    /**
     * 启用观测切面
     * 用于自动为带有@Observed注解的方法创建观测
     */
    @Bean
    @ConditionalOnProperty(value = "management.tracing.enabled", havingValue = "true", matchIfMissing = true)
    public ObservedAspect observedAspect(ObservationRegistry observationRegistry) {
        log.info("Enabling ObservedAspect for automatic method observation");
        return new ObservedAspect(observationRegistry);
    }

    /**
     * 自定义观测注册表配置
     */
    @Bean
    public ObservationRegistry observationRegistry() {
        ObservationRegistry registry = ObservationRegistry.create();
        
        // 添加自定义观测处理器
        registry.observationConfig()
            .observationHandler(new CustomObservationHandler())
            .observationFilter(new BusinessObservationFilter());
            
        log.info("Custom ObservationRegistry configured with business handlers");
        return registry;
    }

    /**
     * 自定义观测处理器
     * 用于处理业务相关的观测事件
     */
    private static class CustomObservationHandler implements io.micrometer.observation.ObservationHandler<io.micrometer.observation.Observation.Context> {
        
        @Override
        public void onStart(io.micrometer.observation.Observation.Context context) {
            // 在观测开始时添加自定义标签
            if (context.getName().startsWith("user.")) {
                context.addLowCardinalityKeyValue(KeyValue.of("service", "user-center"));
                context.addLowCardinalityKeyValue(KeyValue.of("domain", "user-management"));
            }
        }

        @Override
        public void onError(io.micrometer.observation.Observation.Context context) {
            // 记录错误信息
            log.warn("Observation error for: {}", context.getName());
        }

        @Override
        public void onEvent(io.micrometer.observation.Observation.Event event, io.micrometer.observation.Observation.Context context) {
            // 处理观测事件
            log.debug("Observation event: {} for context: {}", event.getName(), context.getName());
        }

        @Override
        public void onScopeOpened(io.micrometer.observation.Observation.Context context) {
            // 作用域打开时的处理
        }

        @Override
        public void onScopeClosed(io.micrometer.observation.Observation.Context context) {
            // 作用域关闭时的处理
        }

        @Override
        public void onStop(io.micrometer.observation.Observation.Context context) {
            // 观测结束时的处理
            log.debug("Observation completed: {}", context.getName());
        }

        @Override
        public boolean supportsContext(io.micrometer.observation.Observation.Context context) {
            return true;
        }
    }

    /**
     * 业务观测过滤器
     * 用于过滤和增强业务相关的观测
     */
    private static class BusinessObservationFilter implements io.micrometer.observation.ObservationFilter {
        
        @Override
        public io.micrometer.observation.Observation.Context map(io.micrometer.observation.Observation.Context context) {
            // 为特定的业务操作添加额外的上下文信息
            String name = context.getName();

            if (name.contains("user.auth")) {
                context.addLowCardinalityKeyValue(KeyValue.of("operation_type", "authentication"));
            } else if (name.contains("user.profile")) {
                context.addLowCardinalityKeyValue(KeyValue.of("operation_type", "profile_management"));
            } else if (name.contains("user.project")) {
                context.addLowCardinalityKeyValue(KeyValue.of("operation_type", "project_management"));
            } else if (name.contains("cache")) {
                context.addLowCardinalityKeyValue(KeyValue.of("operation_type", "cache_operation"));
            }

            return context;
        }
    }
}
