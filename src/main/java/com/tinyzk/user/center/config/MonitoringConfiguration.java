package com.tinyzk.user.center.config;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.config.MeterFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * 监控配置类
 * 配置指标收集、过滤和自定义标签
 */
@Slf4j
@Configuration
@EnableAspectJAutoProxy
public class MonitoringConfiguration {

    /**
     * 自定义MeterRegistry配置
     */
    @Bean
    public MeterRegistryCustomizer<MeterRegistry> metricsCommonTags() {
        return registry -> {
            // 添加通用标签
            registry.config()
                .commonTags(
                    "application", "user-center",
                    "service", "user-management",
                    "environment", getEnvironment()
                )
                // 添加指标过滤器
                .meterFilter(MeterFilter.deny(id -> {
                    // 过滤掉一些不需要的指标
                    String name = id.getName();
                    return name.startsWith("jvm.gc.pause") && 
                           id.getTag("cause") != null && 
                           id.getTag("cause").equals("No GC");
                }))
                .meterFilter(MeterFilter.denyNameStartsWith("tomcat.sessions"))
                .meterFilter(MeterFilter.denyNameStartsWith("spring.security"))
                // 重命名一些指标
                .meterFilter(MeterFilter.renameTag("http.server.requests", "uri", "endpoint"))
                // 限制某些指标的基数
                .meterFilter(MeterFilter.maximumExpectedValue("http.server.requests", 100));

            log.info("MeterRegistry configured with custom tags and filters");
        };
    }

    /**
     * 获取当前环境
     */
    private String getEnvironment() {
        String activeProfile = System.getProperty("spring.profiles.active");
        if (activeProfile == null) {
            activeProfile = System.getenv("SPRING_PROFILES_ACTIVE");
        }
        return activeProfile != null ? activeProfile : "unknown";
    }

    /**
     * 自定义业务指标过滤器
     */
    @Bean
    public MeterFilter businessMetricsFilter() {
        return MeterFilter.accept(id -> {
            String name = id.getName();
            // 只接受业务相关的指标
            return name.startsWith("user.") || 
                   name.startsWith("cache.") || 
                   name.startsWith("database.") || 
                   name.startsWith("redis.") ||
                   name.startsWith("business.");
        });
    }

    /**
     * HTTP请求指标配置
     */
    @Bean
    public MeterFilter httpRequestMetricsFilter() {
        return MeterFilter.accept(id -> {
            if (id.getName().equals("http.server.requests")) {
                String uri = id.getTag("uri");
                // 过滤掉健康检查和静态资源的指标
                return uri != null && 
                       !uri.startsWith("/actuator") && 
                       !uri.startsWith("/static") &&
                       !uri.startsWith("/webjars") &&
                       !uri.equals("/favicon.ico");
            }
            return true;
        });
    }
}
