package com.tinyzk.user.center.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tinyzk.user.center.entity.UserProfile;
import org.apache.ibatis.annotations.Select;
/**
 * 用户个人资料 Mapper 接口
 */
public interface UserProfileMapper extends BaseMapper<UserProfile> {

    @Select("SELECT * FROM user_profile WHERE user_id = #{userId}")
    UserProfile selectByUserId(Long userId);
    
}
