package com.tinyzk.user.center.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tinyzk.user.center.dto.UpdateUserRequestDTO;
import com.tinyzk.user.center.dto.UserListRequestDTO;
import com.tinyzk.user.center.entity.UserBase;
import com.tinyzk.user.center.vo.UserDetailVO;
import com.tinyzk.user.center.vo.UserListVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 管理员用户数据访问接口
 */
public interface AdminUserMapper extends BaseMapper<UserBase> {
    
    /**
     * 分页查询用户列表
     * 
     * @param page 分页参数
     * @param requestDTO 查询条件
     * @return 用户列表分页数据
     */
    IPage<UserListVO> selectUserListPage(Page<UserListVO> page, @Param("dto") UserListRequestDTO requestDTO);
    
    /**
     * 根据用户ID列表查询用户认证信息
     * 
     * @param userIds 用户ID列表
     * @return 用户认证信息列表
     */
    List<UserListVO.IdentifierVO> selectUserIdentifiers(@Param("userIds") List<Long> userIds);
    
    /**
     * 查询用户基础信息
     * 
     * @param userId 用户ID
     * @return 用户基础信息
     */
    UserDetailVO.UserBaseInfo selectUserBaseInfo(@Param("userId") Long userId);
    
    /**
     * 查询用户资料信息
     * 
     * @param userId 用户ID
     * @return 用户资料信息
     */
    UserDetailVO.UserProfileInfo selectUserProfileInfo(@Param("userId") Long userId);
    
    /**
     * 查询用户认证信息
     * 
     * @param userId 用户ID
     * @return 用户认证信息列表
     */
    List<UserDetailVO.UserAuthInfo> selectUserAuthInfo(@Param("userId") Long userId);
    
    /**
     * 查询用户联系方式信息
     * 
     * @param userId 用户ID
     * @return 用户联系方式信息列表
     */
    List<UserDetailVO.UserContactInfo> selectUserContactInfo(@Param("userId") Long userId);
    
    /**
     * 查询用户教育经历信息
     * 
     * @param userId 用户ID
     * @return 用户教育经历信息列表
     */
    List<UserDetailVO.UserEducationInfo> selectUserEducationInfo(@Param("userId") Long userId);
    
    /**
     * 查询用户工作经历信息
     * 
     * @param userId 用户ID
     * @return 用户工作经历信息列表
     */
    List<UserDetailVO.UserWorkInfo> selectUserWorkInfo(@Param("userId") Long userId);
    
    /**
     * 查询用户项目经历信息
     * 
     * @param userId 用户ID
     * @return 用户项目经历信息列表
     */
    List<UserDetailVO.UserProjectInfo> selectUserProjectInfo(@Param("userId") Long userId);
    
    /**
     * 查询用户培训经历信息
     * 
     * @param userId 用户ID
     * @return 用户培训经历信息列表
     */
    List<UserDetailVO.UserTrainingInfo> selectUserTrainingInfo(@Param("userId") Long userId);
    
    /**
     * 查询用户兼职经历信息
     * 
     * @param userId 用户ID
     * @return 用户兼职经历信息列表
     */
    List<UserDetailVO.UserPartTimeInfo> selectUserPartTimeInfo(@Param("userId") Long userId);
    
    /**
     * 更新用户基础信息
     * 
     * @param userId 用户ID
     * @param dto 用户基础信息DTO
     * @return 更新行数
     */
    int updateUserBase(@Param("userId") Long userId, @Param("dto") UpdateUserRequestDTO.UserBaseDTO dto);
    
    /**
     * 更新用户资料信息
     * 
     * @param userId 用户ID
     * @param dto 用户资料信息DTO
     * @return 更新行数
     */
    int updateUserProfile(@Param("userId") Long userId, @Param("dto") UpdateUserRequestDTO.UserProfileDTO dto);
    
    /**
     * 根据ID查询用户
     * 
     * @param userId 用户ID
     * @return 用户实体
     */
    @Select("SELECT * FROM user_base WHERE user_id = #{userId} AND deleted_at IS NULL")
    UserBase selectById(@Param("userId") Long userId);
    
    /**
     * 根据ID更新用户状态
     * 
     * @param userId 用户ID
     * @param status 状态值
     * @return 更新行数
     */
    @Update("UPDATE user_base SET status = #{status}, updated_at = NOW() WHERE user_id = #{userId} AND deleted_at IS NULL")
    int updateUserStatus(@Param("userId") Long userId, @Param("status") Integer status);
}
