package com.tinyzk.user.center.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tinyzk.user.center.entity.UserTraining;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户培训经历Mapper接口
 */
@Mapper
public interface UserTrainingMapper extends BaseMapper<UserTraining> {

    /**
     * 根据用户ID查询培训经历列表
     *
     * @param userId 用户ID
     * @return 培训经历列表
     */
    @Select("SELECT * FROM user_training_history WHERE user_id = #{userId} AND deleted_at IS NULL")
    List<UserTraining> selectByUserId(@Param("userId") Long userId);
} 