package com.tinyzk.user.center.controller;

import com.tinyzk.user.center.common.Result;
import com.tinyzk.user.center.service.BusinessMetricsService;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Meter;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.HealthEndpoint;
import org.springframework.boot.actuate.health.Status;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 监控控制器
 * 提供业务监控数据的API接口
 */
@Slf4j
@RestController
@RequestMapping("/api/monitoring")
@Tag(name = "监控管理", description = "系统监控和指标查询接口")
public class MonitoringController {

    private final BusinessMetricsService businessMetricsService;
    private final MeterRegistry meterRegistry;
    private final HealthEndpoint healthEndpoint;

    @Autowired
    public MonitoringController(BusinessMetricsService businessMetricsService,
                              MeterRegistry meterRegistry,
                              HealthEndpoint healthEndpoint) {
        this.businessMetricsService = businessMetricsService;
        this.meterRegistry = meterRegistry;
        this.healthEndpoint = healthEndpoint;
    }

    /**
     * 获取系统健康状态
     */
    @GetMapping("/health")
    @Operation(summary = "获取系统健康状态", description = "获取系统各组件的健康状态")
    public Result<Map<String, Object>> getSystemHealth() {
        try {
            var health = healthEndpoint.health();
            Map<String, Object> healthInfo = new HashMap<>();
            
            healthInfo.put("status", health.getStatus().getCode());
            healthInfo.put("components", health.getDetails());
            
            // 添加简化的健康状态摘要
            Map<String, String> summary = new HashMap<>();
            summary.put("overall", health.getStatus().getCode());
            
            if (health.getDetails() != null) {
                health.getDetails().forEach((key, value) -> {
                    if (value instanceof Map) {
                        Map<?, ?> componentHealth = (Map<?, ?>) value;
                        Object status = componentHealth.get("status");
                        if (status != null) {
                            summary.put(key, status.toString());
                        }
                    }
                });
            }
            
            healthInfo.put("summary", summary);
            
            return Result.success(healthInfo);
            
        } catch (Exception e) {
            log.error("获取系统健康状态失败", e);
            return Result.error("获取系统健康状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取业务指标概览
     */
    @GetMapping("/metrics/overview")
    @Operation(summary = "获取业务指标概览", description = "获取关键业务指标的概览信息")
    public Result<Map<String, Object>> getMetricsOverview() {
        try {
            Map<String, Object> overview = new HashMap<>();
            
            // 用户相关指标
            Map<String, Double> userMetrics = new HashMap<>();
            userMetrics.put("total_registrations", getCounterValue("user.registration.total"));
            userMetrics.put("total_logins", getCounterValue("user.login.total"));
            userMetrics.put("login_failures", getCounterValue("user.login.failure.total"));
            userMetrics.put("profile_updates", getCounterValue("user.profile.update.total"));
            overview.put("user_metrics", userMetrics);
            
            // 操作相关指标
            Map<String, Double> operationMetrics = new HashMap<>();
            operationMetrics.put("project_operations", getCounterValue("user.project.operation.total"));
            operationMetrics.put("work_history_operations", getCounterValue("user.work.history.operation.total"));
            operationMetrics.put("education_operations", getCounterValue("user.education.operation.total"));
            overview.put("operation_metrics", operationMetrics);
            
            // 系统性能指标
            Map<String, Double> performanceMetrics = new HashMap<>();
            performanceMetrics.put("cache_hits", getCounterValue("cache.hit.total"));
            performanceMetrics.put("cache_misses", getCounterValue("cache.miss.total"));
            performanceMetrics.put("cache_hit_rate", getGaugeValue("cache.hit.rate"));
            performanceMetrics.put("active_sessions", getGaugeValue("user.sessions.active"));
            performanceMetrics.put("pending_operations", getGaugeValue("operations.pending"));
            overview.put("performance_metrics", performanceMetrics);
            
            // 基础设施指标
            Map<String, Double> infrastructureMetrics = new HashMap<>();
            infrastructureMetrics.put("database_connections", getGaugeValue("database.connections.active"));
            infrastructureMetrics.put("redis_connection_status", getGaugeValue("redis.connections.active"));
            infrastructureMetrics.put("total_users", getGaugeValue("user.total.count"));
            overview.put("infrastructure_metrics", infrastructureMetrics);
            
            return Result.success(overview);
            
        } catch (Exception e) {
            log.error("获取业务指标概览失败", e);
            return Result.error("获取业务指标概览失败: " + e.getMessage());
        }
    }

    /**
     * 获取特定指标详情
     */
    @GetMapping("/metrics/{metricName}")
    @Operation(summary = "获取特定指标详情", description = "获取指定指标的详细信息")
    public Result<Map<String, Object>> getMetricDetails(@PathVariable String metricName) {
        try {
            Map<String, Object> details = new HashMap<>();
            
            List<Meter> meters = meterRegistry.getMeters().stream()
                .filter(meter -> meter.getId().getName().equals(metricName))
                .collect(Collectors.toList());
            
            if (meters.isEmpty()) {
                return Result.error("指标不存在: " + metricName);
            }
            
            for (Meter meter : meters) {
                Map<String, Object> meterInfo = new HashMap<>();
                meterInfo.put("name", meter.getId().getName());
                meterInfo.put("type", meter.getId().getType().name());
                meterInfo.put("description", meter.getId().getDescription());
                meterInfo.put("tags", meter.getId().getTags());
                
                // 根据指标类型获取值
                switch (meter.getId().getType()) {
                    case COUNTER:
                        meterInfo.put("value", ((io.micrometer.core.instrument.Counter) meter).count());
                        break;
                    case GAUGE:
                        meterInfo.put("value", ((io.micrometer.core.instrument.Gauge) meter).value());
                        break;
                    case TIMER:
                        io.micrometer.core.instrument.Timer timer = (io.micrometer.core.instrument.Timer) meter;
                        Map<String, Object> timerInfo = new HashMap<>();
                        timerInfo.put("count", timer.count());
                        timerInfo.put("total_time", timer.totalTime(java.util.concurrent.TimeUnit.MILLISECONDS));
                        timerInfo.put("mean", timer.mean(java.util.concurrent.TimeUnit.MILLISECONDS));
                        timerInfo.put("max", timer.max(java.util.concurrent.TimeUnit.MILLISECONDS));
                        meterInfo.put("value", timerInfo);
                        break;
                    default:
                        meterInfo.put("value", "Unsupported meter type");
                }
                
                details.put(meter.getId().toString(), meterInfo);
            }
            
            return Result.success(details);
            
        } catch (Exception e) {
            log.error("获取指标详情失败: {}", metricName, e);
            return Result.error("获取指标详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有可用指标列表
     */
    @GetMapping("/metrics")
    @Operation(summary = "获取所有可用指标", description = "获取系统中所有可用指标的列表")
    public Result<Map<String, Object>> getAllMetrics() {
        try {
            Map<String, Object> allMetrics = new HashMap<>();
            
            // 按类型分组指标
            Map<String, List<String>> metricsByType = meterRegistry.getMeters().stream()
                .collect(Collectors.groupingBy(
                    meter -> meter.getId().getType().name(),
                    Collectors.mapping(meter -> meter.getId().getName(), Collectors.toList())
                ));
            
            allMetrics.put("metrics_by_type", metricsByType);
            allMetrics.put("total_metrics", meterRegistry.getMeters().size());
            
            // 业务指标列表
            List<String> businessMetrics = meterRegistry.getMeters().stream()
                .map(meter -> meter.getId().getName())
                .filter(name -> name.startsWith("user.") || 
                              name.startsWith("cache.") || 
                              name.startsWith("database.") ||
                              name.startsWith("redis."))
                .distinct()
                .sorted()
                .collect(Collectors.toList());
            
            allMetrics.put("business_metrics", businessMetrics);
            
            return Result.success(allMetrics);
            
        } catch (Exception e) {
            log.error("获取指标列表失败", e);
            return Result.error("获取指标列表失败: " + e.getMessage());
        }
    }

    /**
     * 手动触发指标收集
     */
    @PostMapping("/metrics/collect")
    @Operation(summary = "手动触发指标收集", description = "手动触发一次指标收集操作")
    public Result<String> triggerMetricsCollection() {
        try {
            // 这里可以触发一些手动的指标收集操作
            log.info("手动触发指标收集");
            
            // 示例：更新一些实时指标
            businessMetricsService.incrementActiveUserSessions();
            businessMetricsService.decrementActiveUserSessions();
            
            return Result.success("指标收集已触发");
            
        } catch (Exception e) {
            log.error("触发指标收集失败", e);
            return Result.error("触发指标收集失败: " + e.getMessage());
        }
    }

    /**
     * 获取计数器值
     */
    private double getCounterValue(String name) {
        return meterRegistry.find(name).counter() != null ? 
            meterRegistry.find(name).counter().count() : 0.0;
    }

    /**
     * 获取仪表值
     */
    private double getGaugeValue(String name) {
        return meterRegistry.find(name).gauge() != null ? 
            meterRegistry.find(name).gauge().value() : 0.0;
    }
}
