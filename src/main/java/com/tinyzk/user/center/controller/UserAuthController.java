package com.tinyzk.user.center.controller;

import com.tinyzk.user.center.common.Result;
import com.tinyzk.user.center.common.annotation.ClientValidation;
import com.tinyzk.user.center.dto.LoginDTO;
import com.tinyzk.user.center.dto.RegisterDTO;
import com.tinyzk.user.center.service.UserAuthService;
import com.tinyzk.user.center.vo.LoginVO;
import com.tinyzk.user.center.vo.RegisterVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户认证控制器
 * 提供用户登录、注册相关接口
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/auth")
@RequiredArgsConstructor
@Tag(name = "用户认证", description = "用户登录、注册相关接口")
public class UserAuthController {
    
    private final UserAuthService userAuthService;
    
    /**
     * 用户登录
     * 注意：登录接口不添加权限验证，因为用户还未登录
     *
     * @param loginDTO 登录信息
     * @return 登录结果
     */
    @PostMapping("/login")
    @ClientValidation(requireValidClient = false)  // 登录允许匿名客户端，在service层处理
    @Operation(summary = "用户登录", description = "用户通过不同方式（微信、支付宝、抖音小程序等）登录系统")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "登录成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = LoginVO.class))),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "401", description = "凭证验证失败"),
        @ApiResponse(responseCode = "403", description = "无效的客户端身份"),
        @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<LoginVO> login(
            @Parameter(description = "登录信息", required = true) 
            @RequestBody @Validated LoginDTO loginDTO) {
        log.info("用户登录请求: {}", loginDTO);
        LoginVO loginVO = userAuthService.login(loginDTO);
        return Result.success(loginVO, "登录成功");
    }
    
    /**
     * 用户注册
     * 需要验证客户端身份，确保只有有效的客户端才能注册用户
     *
     * @param registerDTO 注册信息
     * @return 注册结果
     */
    @PostMapping("/register")
    @ClientValidation(requireValidClient = true, message = "注册用户需要有效的客户端身份")
    @Operation(summary = "用户注册", description = "用户主动注册（非首次登录的隐式注册）")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "注册成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = RegisterVO.class))),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "403", description = "无效的客户端身份"),
        @ApiResponse(responseCode = "409", description = "用户已存在"),
        @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<RegisterVO> register(
            @Parameter(description = "注册信息", required = true) 
            @RequestBody @Validated RegisterDTO registerDTO) {
        log.info("用户注册请求: {}", registerDTO);
        RegisterVO registerVO = userAuthService.register(registerDTO);
        return Result.success(registerVO, "注册成功");
    }
}
