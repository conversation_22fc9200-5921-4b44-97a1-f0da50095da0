package com.tinyzk.user.center;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;

@EnableDiscoveryClient
@SpringBootApplication
public class UserCenterApplication {

    public static void main(String[] args) {
        ConfigurableApplicationContext context = SpringApplication.run(UserCenterApplication.class, args);
        Environment env = context.getEnvironment();
        String port = env.getProperty("server.port", "8080");
        String contextPath = env.getProperty("server.servlet.context-path", "");
        
        System.out.println("\n====================================================");
        System.out.println("服务启动成功！");
        System.out.println("服务地址: http://localhost:" + port + contextPath);
        System.out.println("API文档: http://localhost:" + port + contextPath + "/doc.html");
        System.out.println("====================================================\n");
    }

}
