package com.tinyzk.user.center.aspect;

import com.tinyzk.user.center.service.BusinessMetricsService;
import io.micrometer.observation.annotation.Observed;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;

/**
 * 指标收集切面
 * 自动为业务方法收集性能指标和业务指标
 */
@Slf4j
@Aspect
@Component
public class MetricsAspect {

    private final BusinessMetricsService businessMetricsService;

    public MetricsAspect(BusinessMetricsService businessMetricsService) {
        this.businessMetricsService = businessMetricsService;
    }

    /**
     * 用户认证相关方法切点
     */
    @Pointcut("execution(* com.tinyzk.user.center.service.UserAuthService.*(..))")
    public void userAuthMethods() {}

    /**
     * 用户资料相关方法切点
     */
    @Pointcut("execution(* com.tinyzk.user.center.service.UserProfileService.*(..))")
    public void userProfileMethods() {}

    /**
     * 用户项目经历相关方法切点
     */
    @Pointcut("execution(* com.tinyzk.user.center.service.UserProjectHistoryService.*(..))")
    public void userProjectMethods() {}

    /**
     * 用户工作经历相关方法切点
     */
    @Pointcut("execution(* com.tinyzk.user.center.service.UserWorkHistoryService.*(..))")
    public void userWorkHistoryMethods() {}

    /**
     * 用户教育经历相关方法切点
     */
    @Pointcut("execution(* com.tinyzk.user.center.service.UserEducationHistoryService.*(..))")
    public void userEducationMethods() {}

    /**
     * 数据库操作相关方法切点
     */
    @Pointcut("execution(* com.tinyzk.user.center.mapper.*.*(..))")
    public void databaseMethods() {}

    /**
     * 缓存操作相关方法切点
     */
    @Pointcut("@annotation(org.springframework.cache.annotation.Cacheable) || " +
              "@annotation(org.springframework.cache.annotation.CacheEvict) || " +
              "@annotation(org.springframework.cache.annotation.CachePut)")
    public void cacheMethods() {}

    /**
     * 用户认证方法监控
     */
    @Around("userAuthMethods()")
    @Observed(name = "user.auth.operation", contextualName = "user-auth")
    public Object monitorUserAuth(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = joinPoint.getSignature().getName();
        Instant start = Instant.now();
        
        try {
            businessMetricsService.incrementPendingOperations();
            Object result = joinPoint.proceed();
            
            // 根据方法名记录不同的指标
            if (methodName.contains("login") || methodName.contains("authenticate")) {
                businessMetricsService.recordUserLogin("standard");
            }
            
            Duration duration = Duration.between(start, Instant.now());
            businessMetricsService.recordUserAuthTime(duration, methodName);
            
            return result;
            
        } catch (Exception e) {
            if (methodName.contains("login") || methodName.contains("authenticate")) {
                businessMetricsService.recordUserLoginFailure(e.getClass().getSimpleName());
            }
            throw e;
        } finally {
            businessMetricsService.decrementPendingOperations();
        }
    }

    /**
     * 用户资料方法监控
     */
    @Around("userProfileMethods()")
    @Observed(name = "user.profile.operation", contextualName = "user-profile")
    public Object monitorUserProfile(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = joinPoint.getSignature().getName();
        
        try {
            businessMetricsService.incrementPendingOperations();
            Object result = joinPoint.proceed();
            
            // 记录用户资料更新操作
            if (methodName.contains("update") || methodName.contains("save") || 
                methodName.contains("create") || methodName.contains("modify")) {
                businessMetricsService.recordUserProfileUpdate(methodName);
            }
            
            return result;
            
        } finally {
            businessMetricsService.decrementPendingOperations();
        }
    }

    /**
     * 用户项目经历方法监控
     */
    @Around("userProjectMethods()")
    @Observed(name = "user.project.operation", contextualName = "user-project")
    public Object monitorUserProject(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = joinPoint.getSignature().getName();
        
        try {
            businessMetricsService.incrementPendingOperations();
            Object result = joinPoint.proceed();
            
            // 记录项目操作
            if (methodName.contains("create")) {
                businessMetricsService.recordUserProjectOperation("create");
            } else if (methodName.contains("update")) {
                businessMetricsService.recordUserProjectOperation("update");
            } else if (methodName.contains("delete")) {
                businessMetricsService.recordUserProjectOperation("delete");
            } else if (methodName.contains("get") || methodName.contains("find")) {
                businessMetricsService.recordUserProjectOperation("read");
            }
            
            return result;
            
        } finally {
            businessMetricsService.decrementPendingOperations();
        }
    }

    /**
     * 用户工作经历方法监控
     */
    @Around("userWorkHistoryMethods()")
    @Observed(name = "user.work.history.operation", contextualName = "user-work-history")
    public Object monitorUserWorkHistory(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = joinPoint.getSignature().getName();
        
        try {
            businessMetricsService.incrementPendingOperations();
            Object result = joinPoint.proceed();
            
            // 记录工作经历操作
            if (methodName.contains("create")) {
                businessMetricsService.recordUserWorkHistoryOperation("create");
            } else if (methodName.contains("update")) {
                businessMetricsService.recordUserWorkHistoryOperation("update");
            } else if (methodName.contains("delete")) {
                businessMetricsService.recordUserWorkHistoryOperation("delete");
            } else if (methodName.contains("get") || methodName.contains("find")) {
                businessMetricsService.recordUserWorkHistoryOperation("read");
            }
            
            return result;
            
        } finally {
            businessMetricsService.decrementPendingOperations();
        }
    }

    /**
     * 用户教育经历方法监控
     */
    @Around("userEducationMethods()")
    @Observed(name = "user.education.operation", contextualName = "user-education")
    public Object monitorUserEducation(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = joinPoint.getSignature().getName();
        
        try {
            businessMetricsService.incrementPendingOperations();
            Object result = joinPoint.proceed();
            
            // 记录教育经历操作
            if (methodName.contains("create")) {
                businessMetricsService.recordUserEducationOperation("create");
            } else if (methodName.contains("update")) {
                businessMetricsService.recordUserEducationOperation("update");
            } else if (methodName.contains("delete")) {
                businessMetricsService.recordUserEducationOperation("delete");
            } else if (methodName.contains("get") || methodName.contains("find")) {
                businessMetricsService.recordUserEducationOperation("read");
            }
            
            return result;
            
        } finally {
            businessMetricsService.decrementPendingOperations();
        }
    }

    /**
     * 数据库操作监控
     */
    @Around("databaseMethods()")
    @Observed(name = "database.operation", contextualName = "database-query")
    public Object monitorDatabaseOperation(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = joinPoint.getSignature().getName();
        String className = joinPoint.getSignature().getDeclaringType().getSimpleName();
        Instant start = Instant.now();
        
        try {
            Object result = joinPoint.proceed();
            
            Duration duration = Duration.between(start, Instant.now());
            businessMetricsService.recordDatabaseQueryTime(duration, className + "." + methodName);
            
            return result;
            
        } catch (Exception e) {
            log.warn("Database operation failed: {}.{}", className, methodName, e);
            throw e;
        }
    }

    /**
     * 缓存操作监控
     */
    @Around("cacheMethods()")
    @Observed(name = "cache.operation", contextualName = "cache-access")
    public Object monitorCacheOperation(ProceedingJoinPoint joinPoint) throws Throwable {
        String className = joinPoint.getSignature().getDeclaringType().getSimpleName();
        
        try {
            Object result = joinPoint.proceed();
            
            // 这里简化处理，实际应该根据缓存注解的具体类型来判断
            if (result != null) {
                businessMetricsService.recordCacheHit(className);
            } else {
                businessMetricsService.recordCacheMiss(className);
            }
            
            return result;
            
        } catch (Exception e) {
            businessMetricsService.recordCacheMiss(className);
            throw e;
        }
    }
}
