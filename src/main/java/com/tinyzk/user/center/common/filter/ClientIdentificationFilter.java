package com.tinyzk.user.center.common.filter;

import com.tinyzk.user.center.common.context.ClientContext;
import com.tinyzk.user.center.entity.OAuthClientDetails;
import com.tinyzk.user.center.service.ClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 客户端识别过滤器，从请求头中获取客户端标识
 */
@Slf4j
@Component
public class ClientIdentificationFilter extends OncePerRequestFilter {

    /**
     * 客户端ID的Header名称
     */
    private static final String CLIENT_ID_HEADER = "X-Client-ID";
    
    /**
     * 客户端服务
     */
    @Autowired
    private ClientService clientService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        
        try {
            // 从请求头中获取客户端ID
            String clientId = request.getHeader(CLIENT_ID_HEADER);
            
            ClientContext clientContext = new ClientContext();
            
            // 验证客户端ID是否有效
            if (StringUtils.hasText(clientId) && clientService.validateClient(clientId)) {
                log.debug("有效的客户端ID: {}", clientId);
                
                // 获取客户端详情
                OAuthClientDetails clientDetails = clientService.getClientDetails(clientId);
                
                // 设置客户端上下文
                clientContext.setClientId(clientId);
                clientContext.setClientDetails(clientDetails);
                clientContext.setAnonymous(false);
                
                // 将客户端ID和详情放入请求属性中，以便后续的处理器使用
                request.setAttribute("currentClientId", clientId);
                request.setAttribute("currentClientDetails", clientDetails);
                
                log.debug("设置客户端上下文: clientId={}, clientName={}", 
                         clientId, clientDetails != null ? clientDetails.getClientName() : "unknown");
                         
            } else {
                // 如果没有提供有效的客户端ID，设置为匿名客户端
                log.debug("无效或缺失的客户端ID: {}, 设置为匿名客户端", clientId);
                
                clientContext.setClientId("anonymous");
                clientContext.setClientDetails(null);
                clientContext.setAnonymous(true);
                
                request.setAttribute("currentClientId", "anonymous");
                request.setAttribute("currentClientDetails", null);
            }
            
            // 设置线程本地的客户端上下文
            ClientContext.setCurrentContext(clientContext);
            
            // 继续处理请求
            filterChain.doFilter(request, response);
            
        } finally {
            // 清除线程本地的客户端上下文，避免内存泄漏
            ClientContext.clearContext();
        }
    }
    
    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) throws ServletException {
        // 可以在这里排除某些不需要客户端验证的路径
        String path = request.getRequestURI();
        
        // 健康检查和监控端点不需要客户端验证
        if (path.startsWith("/actuator/") || 
            path.startsWith("/health") || 
            path.startsWith("/swagger") ||
            path.startsWith("/v3/api-docs")) {
            return true;
        }
        
        return false;
    }
} 