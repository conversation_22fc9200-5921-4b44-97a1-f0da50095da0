package com.tinyzk.user.center.common.exception;

import com.tinyzk.user.center.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 全局异常处理器
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    /**
     * 处理客户端验证异常
     */
    @ExceptionHandler(ClientValidationException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public Result<Void> handleClientValidationException(ClientValidationException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        String clientId = request.getHeader("X-Client-ID");
        
        log.warn("客户端验证失败: uri={}, clientId={}, message={}", requestURI, clientId, e.getMessage());
        
        String enhancedMessage = e.getMessage() + "\n" + buildClientValidationHint();
        return Result.error(enhancedMessage, ErrorCode.FORBIDDEN.getCode());
    }
    
    /**
     * 处理一般业务异常
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleBusinessException(BusinessException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.warn("业务异常: uri={}, code={}, message={}", requestURI, e.getCode(), e.getMessage());
        
        return Result.error(e.getMessage(), e.getCode());
    }
    
    /**
     * 处理系统异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<Void> handleException(Exception e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("系统异常: uri={}", requestURI, e);
        
        return Result.error("系统繁忙，请稍后重试", ErrorCode.SYSTEM_ERROR.getCode());
    }
    
    /**
     * 构建客户端验证提示信息
     */
    private String buildClientValidationHint() {
        return "提示：请确保在请求头中包含有效的 X-Client-ID 参数。" +
               "如果您是开发者，请联系管理员获取有效的客户端标识；" +
               "如果您是用户，请使用官方客户端访问服务。";
    }
}
