package com.tinyzk.user.center.common.exception;

import lombok.Getter;

/**
 * 错误码枚举
 */
@Getter
public enum ErrorCode {
    
    /**
     * 成功
     */
    SUCCESS(200, "操作成功"),
    
    /**
     * 参数错误
     */
    PARAM_ERROR(400, "参数错误"),
    
    /**
     * 未认证
     */
    UNAUTHORIZED(401, "未认证"),
    
    /**
     * 未授权
     */
    FORBIDDEN(403, "未授权"),
    
    /**
     * 资源不存在
     */
    NOT_FOUND(404, "资源不存在"),
    
    /**
     * 业务错误
     */
    BUSINESS_ERROR(500, "业务错误"),
    
    /**
     * 系统错误
     */
    SYSTEM_ERROR(500, "系统错误"),
    
    /**
     * 用户已存在
     */
    USER_ALREADY_EXISTS(1001, "用户已存在"),
    
    /**
     * 用户不存在
     */
    USER_NOT_EXISTS(1002, "用户不存在"),
    
    /**
     * 用户状态异常
     */
    USER_STATUS_ERROR(1003, "用户状态异常"),
    
    /**
     * 凭证验证失败
     */
    CREDENTIAL_VERIFICATION_FAILED(1004, "凭证验证失败"),
    
    /**
     * 姓名格式不合法
     */
    REAL_NAME_INVALID(1005, "姓名格式不合法"),
    
    /**
     * 身份证号格式不合法
     */
    ID_NUMBER_INVALID(1006, "身份证号格式不合法"),
    
    /**
     * 未找到认证记录
     */
    REAL_NAME_AUTH_NOT_FOUND(1007, "未找到实名认证记录"),
    
    /**
     * 用户个人资料不存在
     */
    USER_PROFILE_NOT_FOUND(1008, "用户个人资料不存在");
    
    /**
     * 错误码
     */
    private final Integer code;
    
    /**
     * 错误消息
     */
    private final String message;
    
    ErrorCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
}
