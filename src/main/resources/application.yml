## 服务配置
spring:
  application:
    name: user-center
  profiles:
    active: test

  main:
    allow-bean-definition-overriding: true
  cloud:
    compatibility-verifier:
      enabled: false
    nacos:
      config:
        import-check:
          enabled: true
      discovery:
        enabled: true

management:
  server:
    port: 18080
  health:
    elasticsearch:
      enabled: false
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always
    prometheus:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
    distribution:
      percentiles-histogram:
        "[http.server.requests]": true
      percentiles:
        "[http.server.requests]": 0.5, 0.95, 0.99
    tags:
      application: ${spring.application.name}
  tracing:
    sampling:
      probability: 0.1

# 缓存预热配置
cache:
  warmup:
    enabled: true
    startup-warmup: true
    schedule:
      enabled: true
      cron: "0 0 2 * * ?"
      before-expire: PT2M
    strategy:
      user-detail-count: 1000
      user-list-pages: 5
      page-size: 20
      hot-data-days: 7
      priorities:
        - userDetail
        - userList
        - clientDetails
        - userMapping
    resource:
      thread-pool-size: 5
      timeout: PT30M
      batch-size: 100
      interval: PT0.1S