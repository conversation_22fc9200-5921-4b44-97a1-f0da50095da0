---
description: 
globs: 
alwaysApply: false
---
# Project Structure Overview

This is a Spring Boot application for a User Center.

## Key Directories & Files

*   **Main Application Entry Point**: [`src/main/java/com/tinyzk/user/center/UserCenterApplication.java`](mdc:src/main/java/com/tinyzk/user/center/UserCenterApplication.java)
*   **Configuration Files**:
    *   Primary: [`src/main/resources/application.yml`](mdc:src/main/resources/application.yml)
    *   Environment-specific: [`src/main/resources/application-local.yml`](mdc:src/main/resources/application-local.yml), [`src/main/resources/application-prod.yml`](mdc:src/main/resources/application-prod.yml), [`src/main/resources/application-test.yml`](mdc:src/main/resources/application-test.yml)
    *   Logging: [`src/main/resources/logback-spring.xml`](mdc:src/main/resources/logback-spring.xml)
*   **Core Java Code (`src/main/java/com/tinyzk/user/center/`)**:
    *   [`controller/`](mdc:src/main/java/com/tinyzk/user/center/controller): Handles incoming HTTP requests.
    *   [`service/`](mdc:src/main/java/com/tinyzk/user/center/service): Contains business logic.
    *   [`mapper/`](mdc:src/main/java/com/tinyzk/user/center/mapper): Data Access Layer interfaces (likely MyBatis).
    *   [`entity/`](mdc:src/main/java/com/tinyzk/user/center/entity): Database entities.
    *   [`config/`](mdc:src/main/java/com/tinyzk/user/center/config): Spring configuration classes.
*   **MyBatis Mappers (XML)**: [`src/main/resources/mapper/`](mdc:src/main/resources/mapper)
*   **Database Scripts**:
    *   Schema: [`create.sql`](mdc:create.sql)
*   **Build Configuration**: [`pom.xml`](mdc:pom.xml)
*   **Containerization**: [`Dockerfile`](mdc:Dockerfile)

## Common Workflow

1.  Define API endpoints in the `controller` package.
2.  Implement business logic in the `service` package.
3.  Define data access methods in the `mapper` interfaces (Java) and implement them in the corresponding XML files under `src/main/resources/mapper/`.
4.  Define data structures in `entity`, `dto`, and `vo` packages.
5.  Configure application behavior in `application.yml` and environment-specific profiles.
