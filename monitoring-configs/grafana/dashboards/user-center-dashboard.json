{"dashboard": {"id": null, "title": "用户中心监控仪表板", "tags": ["user-center", "business"], "timezone": "browser", "panels": [{"id": 1, "title": "用户注册趋势", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "targets": [{"expr": "rate(user_registration_total[5m]) * 60", "legendFormat": "注册速率 (每分钟)", "refId": "A"}], "yAxes": [{"label": "注册数/分钟", "min": 0}, {"show": false}], "xAxis": {"show": true}, "legend": {"show": true}}, {"id": 2, "title": "登录成功率", "type": "stat", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "targets": [{"expr": "rate(user_login_total[5m]) / (rate(user_login_total[5m]) + rate(user_login_failure_total[5m])) * 100", "legendFormat": "成功率", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 100, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 80}, {"color": "green", "value": 95}]}}}}, {"id": 3, "title": "缓存性能", "type": "graph", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}, "targets": [{"expr": "cache_hit_rate", "legendFormat": "缓存命中率 (%)", "refId": "A"}, {"expr": "rate(cache_hit_total[5m])", "legendFormat": "缓存命中速率", "refId": "B"}]}], "time": {"from": "now-1h", "to": "now"}, "refresh": "5s", "schemaVersion": 27, "version": 1}}