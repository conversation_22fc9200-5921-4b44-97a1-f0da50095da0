# 用户中心项目变更日志

## [1.0.2] - 2025-05-15

### 新增 (Added)
- (用户管理): 添加用户管理相关功能和服务
- (数据同步): 添加flink数据同步任务
  - 任务1: 快结荐用户，sync-giguser-2-userbase
  - 任务2: 1号职场用户，sync-tzkuser-2-userbase 80w+
  - 任务3: 外部用户映射，user-mapping

### 变更 (Changed)
- (用户资料): 优化用户资料查询接口
- (文档): 更新任务列表并添加用户管理功能PRD文档
### 修复 (Fixed)
- 无


## [1.0.1] - 2025-05-07

### 新增 (Added)

- (用户个人资料): 添加用户个人资料查询与更新功能
- (用户工作经历): 添加用户工作经历管理功能
- (用户项目经历): 新增用户项目经历相关功能模块
- (用户培训经历): 新增用户培训经历相关功能模块
- (用户兼职经历): 新增用户兼职经历相关功能模块
- (用户联系方式): 新增用户联系方式管理功能

### 变更 (Changed)

- (用户资料): 移除工作地点字段并优化用户资料查询
- (文档): 更新任务列表并添加用户管理功能PRD文档

### 修复 (Fixed)

- 无

---

## [1.0.0] - 2025-04-25

### 新增
- 项目初始化与基础框架搭建
  - 创建Spring Boot基础项目结构
  - 配置Maven依赖（Spring Boot, Mybatis Plus, Spring Security等）
  - 配置Nacos服务发现与配置中心
  - 配置Redis和MySQL连接
  - 实现对称加密工具类
  - 配置Spring Security基础框架
  - 实现BCrypt密码加密策略

- 数据库设计与实现
  - 创建用户相关数据库表结构（user_base, user_auth, user_profile等）
  - 实现各实体类与Mapper接口

- 用户基础功能
  - 实现用户登录功能（支持多种方式登录）
  - 实现用户注册功能
  - 实现登录/注册日志记录

- 实名认证功能
  - 实现实名认证服务
  - 设计实名认证API接口
  - 实现实名认证业务逻辑
  - 实现实名认证日志记录

### 变更
- 安全配置优化
  - 禁用CSRF保护
  - 禁用HTTP Basic认证
  - 禁用表单登录
  - 配置无状态Session
  - 开放Swagger和Actuator端点

### 修复
- 初始版本暂无修复内容